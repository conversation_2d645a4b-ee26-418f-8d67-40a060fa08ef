["test_auth.py::test_auth_error_handling", "test_auth.py::test_create_design_endpoint", "test_auth.py::test_health_check", "test_auth.py::test_protected_endpoint_with_invalid_auth", "test_auth.py::test_protected_endpoint_without_auth", "test_auth.py::test_public_designs_with_auth", "test_auth.py::test_public_designs_without_auth", "test_auth.py::test_token_verification_error_handling", "test_auth.py::test_user_designs_endpoint", "test_auth.py::test_user_profile_with_valid_auth", "test_auth.py::test_version"]