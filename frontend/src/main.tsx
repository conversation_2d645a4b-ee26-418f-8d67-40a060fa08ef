import { StrictMode } from 'react'
import ReactDOM from 'react-dom/client'
import { ChakraProvider, Button, Box, HStack } from '@chakra-ui/react'
import {
  Outlet,
  RouterProvider,
  createRootRoute,
  createRoute,
  createRouter,
  <PERSON>,
} from '@tanstack/react-router'

import { system } from './theme.ts'
import './styles.css'
import reportWebVitals from './reportWebVitals.ts'
import { AuthProvider, useAuth } from './context/AuthContext.jsx'
import { LoginForm } from './components/LoginForm.jsx'
import { ProtectedRoute } from './components/ProtectedRoute.jsx'

import App from './App.tsx'

const rootRoute = createRootRoute({
  component: RootComponent,
  context: {
    auth: undefined!,
  },
})

function RootComponent() {
  const auth = useAuth()

  return (
    <>
      <Box as="header" bg="blue.500" color="white" p={4}>
        <HStack justify="space-between">
          <Link to="/">
            <Button variant="ghost" color="white">
              Home
            </Button>
          </Link>
          <HStack>
            {auth.user ? (
              <>
                <Link to="/dashboard">
                  <Button variant="ghost" color="white">
                    Dashboard
                  </Button>
                </Link>
                <Button variant="ghost" color="white" onClick={() => auth.signOut()}>
                  Sign Out ({auth.user.email})
                </Button>
              </>
            ) : (
              <Link to="/login">
                <Button variant="ghost" color="white">
                  Login
                </Button>
              </Link>
            )}
          </HStack>
        </HStack>
      </Box>
      <Outlet />
      {/* <TanStackRouterDevtools /> */}
    </>
  )
}

const indexRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/',
  component: App,
})

const loginRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/login',
  component: LoginPage,
})

const dashboardRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/dashboard',
  component: DashboardPage,
})

function LoginPage() {
  return <LoginForm />
}

function DashboardPage() {
  return (
    <ProtectedRoute>
      <Box p={8}>
        <h1>Dashboard</h1>
        <p>Welcome to your dashboard!</p>
      </Box>
    </ProtectedRoute>
  )
}

const routeTree = rootRoute.addChildren([indexRoute, loginRoute, dashboardRoute])

const router = createRouter({
  routeTree,
  context: {
    auth: undefined!,
  },
  defaultPreload: 'intent',
  scrollRestoration: true,
  defaultStructuralSharing: true,
  defaultPreloadStaleTime: 0,
})

declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router
  }
}

const rootElement = document.getElementById('app')
if (rootElement && !rootElement.innerHTML) {
  const root = ReactDOM.createRoot(rootElement)
  root.render(
    <StrictMode>
      <ChakraProvider value={system}>
        <AuthProvider>
          <RouterProvider router={router} />
        </AuthProvider>
      </ChakraProvider>
    </StrictMode>,
  )
}

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals()
